/* ===== ROOT VARIABLES ===== */
:root {
    /* Elegant Light Color Palette */
    --primary-color: #4f46e5;
    --secondary-color: #7c3aed;
    --accent-color: #0ea5e9;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;

    /* Light Theme Colors */
    --light-primary: #f8fafc;
    --light-secondary: #f1f5f9;
    --light-accent: #e2e8f0;
    --pure-white: #ffffff;

    /* Text Colors */
    --text-light: #ffffff;
    --text-dark: #334155;
    --text-muted: #64748b;
    --text-secondary: #475569;
    --text-primary: #1e293b;

    /* Elegant Light Gradients */
    --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    --gradient-secondary: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    --gradient-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-hero: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
    --gradient-card: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.8) 100%);
    --gradient-text: linear-gradient(135deg, #1e1b4b 0%, #4c1d95 25%, #1e3a8a 50%, #0f172a 75%, #000000 100%);
    --gradient-soft: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);

    /* Soft Shadows */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(79, 70, 229, 0.15);
    --shadow-soft: 0 2px 4px rgba(0, 0, 0, 0.05);

    /* Modern Styling */
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    background: var(--gradient-dark);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-content {
    text-align: center;
    color: var(--text-light);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    font-size: 1.2rem;
    font-weight: 300;
    letter-spacing: 2px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* ===== NAVIGATION ===== */
.navbar {
    background: rgba(26, 29, 58, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.navbar.scrolled {
    background: rgba(26, 29, 58, 0.98);
    padding: 0.5rem 0;
    box-shadow: var(--shadow-medium);
}

.navbar-brand {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-light) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.brand-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link.btn {
    background: var(--gradient-primary);
    color: var(--text-light) !important;
    border: none;
    box-shadow: var(--shadow-light);
}

.navbar-nav .nav-link.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: var(--text-light) !important;
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

/* Hero Background */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-soft);
    opacity: 0.6;
    animation: float 20s infinite ease-in-out;
    backdrop-filter: blur(20px);
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.08) 0%, rgba(124, 58, 237, 0.08) 100%);
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: -5s;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08) 0%, rgba(59, 130, 246, 0.08) 100%);
}

.shape-3 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 20%;
    animation-delay: -10s;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.06) 0%, rgba(14, 165, 233, 0.06) 100%);
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: -15s;
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.06) 0%, rgba(79, 70, 229, 0.06) 100%);
}

.shape-5 {
    width: 250px;
    height: 250px;
    bottom: 10%;
    right: 10%;
    animation-delay: -7s;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.95) 0%,
        rgba(241, 245, 249, 0.9) 50%,
        rgba(226, 232, 240, 0.85) 100%);
}

/* Hero Content */
.hero-content {
    position: relative;
    z-index: 10;
    color: var(--text-primary);
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(79, 70, 229, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 70, 229, 0.2);
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.hero-badge:hover {
    background: rgba(79, 70, 229, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.hero-title .gradient-text {
    color: #1e1b4b !important; /* Much darker fallback color */
    background: linear-gradient(135deg, #1e1b4b 0%, #4c1d95 25%, #1e3a8a 50%, #0f172a 75%, #000000 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    position: relative;
    font-weight: 800 !important;
    display: inline-block;
}

.gradient-text {
    color: #1e1b4b; /* Much darker fallback color */
    background: linear-gradient(135deg, #1e1b4b 0%, #4c1d95 25%, #1e3a8a 50%, #0f172a 75%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    font-weight: 800;
    display: inline-block;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: var(--text-muted);
    margin-bottom: 2.5rem;
    max-width: 500px;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(79, 70, 229, 0.1);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    background: rgba(255, 255, 255, 0.95);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1e1b4b; /* Much darker fallback color */
    background: linear-gradient(135deg, #1e1b4b 0%, #4c1d95 25%, #1e3a8a 50%, #0f172a 75%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-primary-hero {
    position: relative;
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    transition: var(--transition);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.btn-primary-hero:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    color: var(--text-light);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary-hero:hover .btn-shine {
    left: 100%;
}

.btn-secondary-hero {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    border: 1px solid rgba(79, 70, 229, 0.2);
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
}

.btn-secondary-hero:hover {
    background: rgba(255, 255, 255, 1);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    z-index: 10;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.articles-showcase {
    position: relative;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 1.5rem;
    max-width: 500px;
}

/* Featured Articles */
.featured-article {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(79, 70, 229, 0.1);
    transition: var(--transition-slow);
    cursor: pointer;
    box-shadow: var(--shadow-soft);
}

.featured-article:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.featured-article.main {
    grid-row: 1 / 3;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(79, 70, 229, 0.15);
}

.featured-article.secondary {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(241, 245, 249, 0.85) 100%);
    border: 1px solid rgba(14, 165, 233, 0.1);
}

.article-image {
    width: 100%;
    height: 100%;
    position: relative;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.article-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(79, 70, 229, 0.85) 0%,
        rgba(124, 58, 237, 0.8) 50%,
        rgba(14, 165, 233, 0.75) 100%);
    z-index: 1;
}

.article-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 3;
    color: white;
    backdrop-filter: blur(10px);
}

.article-category.lifestyle {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.article-category.technology {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.article-category.travel {
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
}

.article-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    z-index: 2;
    color: white;
}

.article-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.featured-article.main .article-title {
    font-size: 1.3rem;
}

.article-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    opacity: 0.9;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Floating Cards */
.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(79, 70, 229, 0.1);
    transition: var(--transition);
    animation: floatCard 6s ease-in-out infinite;
}

.floating-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    background: rgba(255, 255, 255, 1);
}

.card-1 {
    top: 20%;
    right: -20%;
    animation-delay: 0s;
    border: 1px solid rgba(79, 70, 229, 0.15);
}

.card-2 {
    bottom: 20%;
    left: -20%;
    animation-delay: 3s;
    border: 1px solid rgba(14, 165, 233, 0.15);
}

.card-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.card-text {
    color: var(--text-primary);
}

.card-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.card-label {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--text-muted);
    z-index: 10;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(79, 70, 229, 0.1);
}

.scroll-text {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--text-secondary);
}

.scroll-arrow {
    animation: bounce 2s infinite;
    color: var(--primary-color);
}

.scroll-arrow i {
    font-size: 1.2rem;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-30px) rotate(120deg);
    }
    66% {
        transform: translateY(30px) rotate(240deg);
    }
}

@keyframes floatCard {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.1"/><stop offset="100%" stop-color="%23764ba2" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--text-light);
    text-align: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-hero {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-hero.primary {
    background: var(--gradient-primary);
    color: var(--text-light);
    box-shadow: var(--shadow-medium);
}

.btn-hero.secondary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* ===== FEATURED POSTS ===== */
.featured-post {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    background: white;
}

.featured-post:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.featured-post.large {
    height: 400px;
}

.featured-post.medium {
    height: 180px;
}

.post-image {
    position: relative;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.post-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    z-index: 1;
}

.post-category {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-dark);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 3;
    backdrop-filter: blur(10px);
}

.post-category.lifestyle {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.post-category.business {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.post-category.technology {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

.post-category.travel {
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
}

.post-category.health {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
}

.post-category.food {
    background: linear-gradient(135deg, #dc2626 0%, #f59e0b 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.post-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 30px 20px 20px;
    z-index: 2;
}

.post-content {
    color: white;
}

.post-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.3;
}

.featured-post.medium .post-title {
    font-size: 1.1rem;
}

.post-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    opacity: 0.9;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* ===== FLOATING ELEMENTS ===== */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 15s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: -5s;
}

.floating-element:nth-child(3) {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 20%;
    animation-delay: -10s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-30px) rotate(120deg);
    }
    66% {
        transform: translateY(30px) rotate(240deg);
    }
}

/* ===== CTA BANNER ===== */
.cta-banner {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.cta-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cta-content {
    position: relative;
    z-index: 2;
    color: white;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.cta-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-cta {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    display: inline-flex;
    align-items: center;
}

.btn-cta:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* ===== MAIN CONTENT SECTION ===== */
.main-content-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.main-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.post-item {
    border-bottom: 1px solid #e9ecef;
    transition: var(--transition);
}

.post-item:last-child {
    border-bottom: none;
}

.post-item:hover {
    background: #f8f9fa;
}

.large-post {
    display: flex;
    flex-direction: column;
}

.large-post .post-image {
    height: 300px;
    background: var(--gradient-primary);
    position: relative;
}

.large-post .post-content {
    padding: 2rem;
}

.medium-post {
    display: flex;
    align-items: center;
}

.medium-post .post-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
    background: var(--gradient-secondary);
}

.medium-post .post-content {
    padding: 1.5rem;
    flex: 1;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
    padding-left: 2rem;
}

.sidebar-widget {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.sidebar-widget:hover {
    box-shadow: var(--shadow-medium);
}

.sidebar-widget .widget-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    position: relative;
    padding-bottom: 10px;
}

.sidebar-widget .widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.search-form .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.search-form .form-control {
    border: none;
    padding: 1rem;
    background: #f8f9fa;
}

.search-form .form-control:focus {
    box-shadow: none;
    background: white;
}

.recent-post-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    transition: var(--transition);
}

.recent-post-item:last-child {
    border-bottom: none;
}

.recent-post-item:hover {
    background: #f8f9fa;
    margin: 0 -1rem;
    padding: 1rem;
    border-radius: 8px;
}

.recent-post-image {
    width: 60px;
    height: 60px;
    margin-right: 1rem;
    flex-shrink: 0;
}

.post-thumb {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 8px;
}

.recent-post-content {
    flex: 1;
}

.recent-post-title {
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.recent-post-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.recent-post-title a:hover {
    color: var(--primary-color);
}

.recent-post-meta {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.categories-list {
    list-style: none;
    padding: 0;
}

.categories-list li {
    margin-bottom: 0.8rem;
}

.categories-list a {
    color: var(--text-dark);
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    transition: var(--transition);
    border-bottom: 1px solid transparent;
}

.categories-list a:hover {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.categories-list span {
    background: var(--gradient-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.tags-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: #f8f9fa;
    color: var(--text-dark);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
    border: 1px solid #e9ecef;
}

.tag:hover {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

/* ===== VIEW ALL POSTS SECTION ===== */
.view-all-posts {
    background: var(--gradient-primary);
    padding: 4rem 0;
    text-align: center;
}

.view-all-content {
    color: white;
}

.view-all-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    letter-spacing: 2px;
}

.btn-view-all {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    display: inline-flex;
    align-items: center;
}

.btn-view-all:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* ===== SCROLL ANIMATIONS ===== */
[data-aos] {
    pointer-events: none;
}

[data-aos].aos-animate {
    pointer-events: auto;
}

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: var(--text-light);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* ===== FOOTER SECTION ===== */
.footer-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: var(--text-light);
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
}

.footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
}

.footer-widget {
    margin-bottom: 2rem;
}

.widget-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-light);
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.widget-text {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.widget-links {
    list-style: none;
    padding: 0;
}

.widget-links li {
    margin-bottom: 0.8rem;
}

.widget-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    padding-left: 15px;
}

.widget-links a::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-size: 0.8rem;
    transition: var(--transition);
}

.widget-links a:hover {
    color: var(--primary-color);
    padding-left: 20px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.social-links a:hover {
    background: var(--gradient-primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.newsletter-form {
    margin-top: 1.5rem;
}

.newsletter-form .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.newsletter-form .form-control {
    border: none;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    backdrop-filter: blur(10px);
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form .form-control:focus {
    box-shadow: none;
    background: rgba(255, 255, 255, 0.15);
}

.newsletter-form .btn {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 1.5rem;
    transition: var(--transition);
}

.newsletter-form .btn:hover {
    transform: scale(1.05);
}

.footer-divider {
    border: none;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 3rem 0 2rem;
}

.copyright-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    margin: 0;
}

/* ===== BLOG SECTION STYLES ===== */
.blog-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    text-align: center;
    color: var(--text-muted);
    font-size: 1.2rem;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.blog-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.blog-card-image {
    height: 250px;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.blog-card-content {
    padding: 2rem;
}

.blog-card-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.blog-card-excerpt {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.blog-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.read-more-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.read-more-btn:hover {
    color: var(--secondary-color);
}

/* ===== CONTACT FORM STYLES ===== */
.contact-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: #f8f9fa;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.contact-form .form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* ===== ADDITIONAL ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-in {
    animation: fadeInUp 0.8s ease-out forwards;
}

/* ===== HOVER EFFECTS ===== */
.blog-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.blog-card:hover::before {
    opacity: 0.05;
}

.navbar-brand:hover .brand-text {
    animation: pulse 1s ease-in-out;
}

/* ===== GRADIENT TEXT EFFECTS ===== */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-navy);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}

/* ===== PAGE HEADER ===== */
.page-header {
    background: var(--gradient-hero);
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%234f46e5" stop-opacity="0.1"/><stop offset="100%" stop-color="%237c3aed" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

.page-header-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--text-primary);
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1e1b4b; /* Much darker fallback color */
    background: linear-gradient(135deg, #1e1b4b 0%, #4c1d95 25%, #1e3a8a 50%, #0f172a 75%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

.breadcrumb {
    justify-content: center;
    background: transparent;
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-secondary);
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted);
}

/* ===== BLOG LISTING STYLES ===== */
.blog-listing-section {
    padding: 5rem 0;
    background: var(--light-primary);
}

.blog-post-item {
    background: var(--pure-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    margin-bottom: 3rem;
    transition: var(--transition);
    border: 1px solid rgba(79, 70, 229, 0.05);
}

.blog-post-item:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
    border-color: rgba(79, 70, 229, 0.1);
}

.blog-post-item .post-image {
    height: 250px;
    position: relative;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: rgba(255, 255, 255, 0.6);
    font-size: 3rem;
}

.blog-post-item .post-content {
    padding: 2rem;
}

.blog-post-item .post-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.blog-post-item .post-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
}

.blog-post-item .post-title a:hover {
    color: var(--primary-color);
}

.blog-post-item .post-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.blog-post-item .post-excerpt {
    color: var(--text-muted);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.pagination-wrapper {
    margin-top: 3rem;
}

.pagination .page-link {
    color: var(--text-dark);
    border: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: var(--transition);
}

.pagination .page-link:hover {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
}

.pagination .page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
}

/* ===== SINGLE POST STYLES ===== */
.single-post-section {
    padding: 5rem 0;
    background: var(--light-primary);
}

.single-post {
    background: var(--pure-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    margin-bottom: 3rem;
    border: 1px solid rgba(79, 70, 229, 0.05);
}

.post-header {
    padding: 2rem 2rem 1rem;
    position: relative;
}

.post-featured-image {
    height: 400px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.single-post .post-content {
    padding: 2rem;
}

.single-post .post-content .lead {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.single-post .post-content h3 {
    color: var(--text-primary);
    margin: 2rem 0 1rem;
    font-size: 1.5rem;
}

.single-post .post-content ul {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.single-post .post-content li {
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

.post-quote {
    background: var(--light-secondary);
    border-left: 4px solid var(--primary-color);
    padding: 2rem;
    margin: 2rem 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.post-quote p {
    font-size: 1.2rem;
    font-style: italic;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.post-quote cite {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 600;
}

/* ===== POST TAGS & SHARING ===== */
.post-tags, .post-share {
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
}

.post-tags h5, .post-share h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.share-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    color: white;
}

.share-btn.facebook {
    background: #3b5998;
}

.share-btn.twitter {
    background: #1da1f2;
}

.share-btn.linkedin {
    background: #0077b5;
}

.share-btn.pinterest {
    background: #bd081c;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

/* ===== AUTHOR BIO ===== */
.author-bio {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    margin-bottom: 3rem;
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.author-avatar {
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.author-info {
    flex: 1;
}

.author-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.author-description {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.author-social {
    display: flex;
    gap: 0.5rem;
}

.author-social a {
    width: 35px;
    height: 35px;
    background: #f8f9fa;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.author-social a:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-2px);
}

/* ===== RELATED POSTS ===== */
.related-posts {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    margin-bottom: 3rem;
}

.related-post-item {
    display: flex;
    gap: 1rem;
    transition: var(--transition);
    padding: 1rem;
    border-radius: 8px;
}

.related-post-item:hover {
    background: #f8f9fa;
}

.related-post-image {
    width: 100px;
    height: 80px;
    flex-shrink: 0;
    border-radius: 8px;
    overflow: hidden;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.related-post-content {
    flex: 1;
}

.related-post-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.related-post-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.related-post-title a:hover {
    color: var(--primary-color);
}

.related-post-meta {
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* ===== COMMENTS SECTION ===== */
.comments-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
}

.comments-list {
    margin-bottom: 3rem;
}

.comment-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.comment-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.comment-avatar .avatar-placeholder {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.comment-date {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.comment-text {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.comment-actions {
    display: flex;
    gap: 1rem;
}

.reply-btn, .like-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.reply-btn:hover, .like-btn:hover {
    color: var(--primary-color);
}

.comment-reply {
    margin-top: 1.5rem;
    margin-left: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #f0f0f0;
}

.comment-form-section {
    border-top: 2px solid #e9ecef;
    padding-top: 2rem;
}

.form-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

/* ===== ABOUT PAGE STYLES ===== */
.about-content-section {
    padding: 5rem 0;
    background: var(--pure-white);
}

.about-image-container {
    position: relative;
    height: 400px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.about-image {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.4);
    font-size: 6rem;
}

.about-text {
    padding-left: 2rem;
}

.mission-card, .vision-card {
    background: var(--pure-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-soft);
    text-align: center;
    transition: var(--transition);
    height: 100%;
    border: 1px solid rgba(79, 70, 229, 0.05);
}

.mission-card:hover, .vision-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: rgba(79, 70, 229, 0.1);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.card-text {
    color: var(--text-muted);
    line-height: 1.6;
}

.team-card {
    background: var(--pure-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    text-align: center;
    border: 1px solid rgba(79, 70, 229, 0.05);
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: rgba(79, 70, 229, 0.1);
}

.team-image {
    height: 250px;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.4);
    font-size: 4rem;
}

.team-content {
    padding: 2rem;
}

.team-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.team-role {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 1rem;
}

.team-bio {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.team-social {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.team-social a {
    width: 40px;
    height: 40px;
    background: var(--light-secondary);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.team-social a:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-2px);
}

.values-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.values-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.value-card {
    text-align: center;
    position: relative;
    z-index: 2;
}

.value-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.8rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.value-card:hover .value-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.value-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.value-text {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.cta-section {
    background: var(--light-primary);
    padding: 5rem 0;
}

.cta-section .cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.cta-section .cta-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== CONTACT PAGE STYLES ===== */
.contact-content-section {
    padding: 5rem 0;
    background: var(--light-primary);
}

.contact-form-container {
    background: var(--pure-white);
    border-radius: var(--border-radius);
    padding: 3rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    border: 1px solid rgba(79, 70, 229, 0.05);
}

.contact-info {
    background: var(--pure-white);
    border-radius: var(--border-radius);
    padding: 3rem;
    box-shadow: var(--shadow-medium);
    height: fit-content;
    border: 1px solid rgba(79, 70, 229, 0.05);
}

.info-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.info-subtitle {
    color: var(--text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--light-accent);
}

.contact-item:last-of-type {
    border-bottom: none;
    margin-bottom: 2rem;
    padding-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-muted);
    line-height: 1.6;
    margin: 0;
}

.contact-social h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.map-section {
    background: #f8f9fa;
    padding: 0;
}

.map-container {
    height: 400px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.map-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.map-content i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.map-content h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.map-content p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.btn-map {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    display: inline-flex;
    align-items: center;
}

.btn-map:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-2px);
}

.faq-section {
    padding: 5rem 0;
    background: var(--pure-white);
}

.accordion-item {
    border: 1px solid var(--light-accent);
    border-radius: var(--border-radius) !important;
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-button {
    background: var(--pure-white);
    color: var(--text-primary);
    font-weight: 600;
    padding: 1.5rem;
    border: none;
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    background: var(--gradient-primary);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23334155'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
    padding: 1.5rem;
    color: var(--text-muted);
    line-height: 1.6;
    background: var(--light-secondary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-hero {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .social-links {
        justify-content: center;
    }

    .footer-widget {
        text-align: center;
    }

    .widget-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .contact-form-container {
        padding: 2rem !important;
    }

    .floating-element {
        display: none;
    }

    .sidebar {
        padding-left: 0;
        margin-top: 3rem;
    }

    .author-bio {
        flex-direction: column;
        text-align: center;
    }

    .related-post-item {
        flex-direction: column;
    }

    .related-post-image {
        width: 100%;
        height: 200px;
    }

    .about-text {
        padding-left: 0;
        margin-top: 2rem;
    }

    .contact-info {
        margin-top: 2rem;
    }

    .share-buttons {
        justify-content: center;
    }

    .comment-reply {
        margin-left: 0;
    }

    /* Hero Section Mobile */
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .hero-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-primary-hero,
    .btn-secondary-hero {
        text-align: center;
        justify-content: center;
    }

    .hero-visual {
        height: 400px;
        margin-top: 2rem;
    }

    .articles-showcase {
        grid-template-columns: 1fr;
        grid-template-rows: 2fr 1fr 1fr;
        max-width: 300px;
    }

    .floating-card {
        display: none;
    }

    .shape {
        display: none;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: row;
        justify-content: space-around;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-visual {
        height: 300px;
    }

    .articles-showcase {
        max-width: 250px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .blog-card-content {
        padding: 1.5rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }
}
