@extends('website.layout.master')

@section('title', 'Best Template Website For HTML CSS - ModernBlog')

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="page-header-content" data-aos="fade-up">
                    <h1 class="page-title">Post Details</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('blog.index') }}">Blog Entries</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Post Details</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Single Post Content -->
<section class="single-post-section">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <article class="single-post" data-aos="fade-up">
                    <!-- Post Header -->
                    <div class="post-header">
                        <div class="post-category lifestyle">LIFESTYLE</div>
                        <h1 class="post-title">Best Template Website For HTML CSS</h1>
                        <div class="post-meta">
                            <div class="meta-item">
                                <i class="fas fa-user"></i>
                                <span>Admin</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span>May 12, 2024</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-comments"></i>
                                <span>24 Comments</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-eye"></i>
                                <span>1,234 Views</span>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image -->
                    <div class="post-featured-image">
                        <div class="image-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="post-content">
                        <p class="lead">
                            Stand Blog is a free HTML CSS template for your CMS theme. You can easily adapt or customize 
                            it for any kind of CMS or website builder. You are allowed to use it for your business.
                        </p>

                        <p>
                            You are NOT allowed to re-distribute the template ZIP file on any template collection websites. 
                            You can use this template for your commercial websites. Please contact TemplateMo for more 
                            information about licensing terms.
                        </p>

                        <h3>Template Features</h3>
                        <p>
                            This template comes with several amazing features that make it perfect for modern blogs and 
                            content websites. The responsive design ensures your content looks great on all devices, 
                            from desktop computers to mobile phones.
                        </p>

                        <ul>
                            <li>Fully responsive design</li>
                            <li>Clean and modern layout</li>
                            <li>Cross-browser compatibility</li>
                            <li>SEO-friendly structure</li>
                            <li>Easy to customize</li>
                            <li>Well-documented code</li>
                        </ul>

                        <blockquote class="post-quote">
                            <p>
                                "Design is not just what it looks like and feels like. Design is how it works."
                            </p>
                            <cite>- Steve Jobs</cite>
                        </blockquote>

                        <h3>Getting Started</h3>
                        <p>
                            To get started with this template, simply download the ZIP file and extract it to your 
                            desired location. The template includes all necessary HTML, CSS, and JavaScript files 
                            to get you up and running quickly.
                        </p>

                        <p>
                            The template structure is well-organized and follows best practices for web development. 
                            You can easily modify the colors, fonts, and layout to match your brand identity.
                        </p>

                        <h3>Customization Options</h3>
                        <p>
                            One of the best things about this template is how easy it is to customize. The CSS is 
                            well-structured and commented, making it simple to find and modify the styles you need.
                        </p>

                        <p>
                            Whether you're a beginner or an experienced developer, you'll find this template easy 
                            to work with and adapt to your specific needs.
                        </p>
                    </div>

                    <!-- Post Tags -->
                    <div class="post-tags">
                        <h5>Tags:</h5>
                        <div class="tags-list">
                            <a href="#" class="tag">HTML5</a>
                            <a href="#" class="tag">CSS3</a>
                            <a href="#" class="tag">Template</a>
                            <a href="#" class="tag">Responsive</a>
                            <a href="#" class="tag">Modern</a>
                        </div>
                    </div>

                    <!-- Social Share -->
                    <div class="post-share">
                        <h5>Share this post:</h5>
                        <div class="share-buttons">
                            <a href="#" class="share-btn facebook">
                                <i class="fab fa-facebook-f"></i>
                                <span>Facebook</span>
                            </a>
                            <a href="#" class="share-btn twitter">
                                <i class="fab fa-twitter"></i>
                                <span>Twitter</span>
                            </a>
                            <a href="#" class="share-btn linkedin">
                                <i class="fab fa-linkedin-in"></i>
                                <span>LinkedIn</span>
                            </a>
                            <a href="#" class="share-btn pinterest">
                                <i class="fab fa-pinterest"></i>
                                <span>Pinterest</span>
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Author Bio -->
                <div class="author-bio" data-aos="fade-up" data-aos-delay="100">
                    <div class="author-avatar">
                        <div class="avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="author-info">
                        <h4 class="author-name">Admin</h4>
                        <p class="author-description">
                            A passionate writer and content creator with over 5 years of experience in digital marketing 
                            and web development. Loves sharing knowledge about the latest trends in technology and design.
                        </p>
                        <div class="author-social">
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>

                <!-- Related Posts -->
                <div class="related-posts" data-aos="fade-up" data-aos-delay="200">
                    <h3 class="section-title">Related Posts</h3>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <article class="related-post-item">
                                <div class="related-post-image">
                                    <div class="image-placeholder">
                                        <i class="fas fa-image"></i>
                                    </div>
                                </div>
                                <div class="related-post-content">
                                    <h5 class="related-post-title">
                                        <a href="#">The Future of Web Development</a>
                                    </h5>
                                    <div class="related-post-meta">
                                        <span>May 10, 2024</span>
                                    </div>
                                </div>
                            </article>
                        </div>
                        <div class="col-md-6 mb-4">
                            <article class="related-post-item">
                                <div class="related-post-image">
                                    <div class="image-placeholder">
                                        <i class="fas fa-image"></i>
                                    </div>
                                </div>
                                <div class="related-post-content">
                                    <h5 class="related-post-title">
                                        <a href="#">Design Trends That Matter</a>
                                    </h5>
                                    <div class="related-post-meta">
                                        <span>May 8, 2024</span>
                                    </div>
                                </div>
                            </article>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="comments-section" data-aos="fade-up" data-aos-delay="300">
                    <h3 class="section-title">Comments (24)</h3>
                    
                    <!-- Existing Comments -->
                    <div class="comments-list">
                        <!-- Comment 1 -->
                        <div class="comment-item">
                            <div class="comment-avatar">
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>
                            <div class="comment-content">
                                <div class="comment-header">
                                    <h5 class="comment-author">John Doe</h5>
                                    <span class="comment-date">May 13, 2024 at 2:30 PM</span>
                                </div>
                                <p class="comment-text">
                                    This is an excellent template! I've been using it for my blog and it works perfectly. 
                                    The design is clean and modern, and it's very easy to customize.
                                </p>
                                <div class="comment-actions">
                                    <button class="reply-btn">
                                        <i class="fas fa-reply me-1"></i>
                                        Reply
                                    </button>
                                    <button class="like-btn">
                                        <i class="fas fa-thumbs-up me-1"></i>
                                        Like (5)
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Comment 2 -->
                        <div class="comment-item">
                            <div class="comment-avatar">
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>
                            <div class="comment-content">
                                <div class="comment-header">
                                    <h5 class="comment-author">Sarah Wilson</h5>
                                    <span class="comment-date">May 13, 2024 at 4:15 PM</span>
                                </div>
                                <p class="comment-text">
                                    Great article! I'm new to web development and this template has been incredibly 
                                    helpful in understanding modern CSS techniques. Thank you for sharing!
                                </p>
                                <div class="comment-actions">
                                    <button class="reply-btn">
                                        <i class="fas fa-reply me-1"></i>
                                        Reply
                                    </button>
                                    <button class="like-btn">
                                        <i class="fas fa-thumbs-up me-1"></i>
                                        Like (3)
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Comment 3 with Reply -->
                        <div class="comment-item">
                            <div class="comment-avatar">
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>
                            <div class="comment-content">
                                <div class="comment-header">
                                    <h5 class="comment-author">Mike Johnson</h5>
                                    <span class="comment-date">May 14, 2024 at 9:20 AM</span>
                                </div>
                                <p class="comment-text">
                                    Does this template work well with WordPress? I'm looking for a good theme to 
                                    convert this design into a WordPress template.
                                </p>
                                <div class="comment-actions">
                                    <button class="reply-btn">
                                        <i class="fas fa-reply me-1"></i>
                                        Reply
                                    </button>
                                    <button class="like-btn">
                                        <i class="fas fa-thumbs-up me-1"></i>
                                        Like (2)
                                    </button>
                                </div>

                                <!-- Reply -->
                                <div class="comment-reply">
                                    <div class="comment-avatar">
                                        <div class="avatar-placeholder">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="comment-content">
                                        <div class="comment-header">
                                            <h5 class="comment-author">Admin</h5>
                                            <span class="comment-date">May 14, 2024 at 11:45 AM</span>
                                        </div>
                                        <p class="comment-text">
                                            Yes, this template can be easily converted to a WordPress theme. The HTML 
                                            structure is clean and follows WordPress coding standards.
                                        </p>
                                        <div class="comment-actions">
                                            <button class="like-btn">
                                                <i class="fas fa-thumbs-up me-1"></i>
                                                Like (1)
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comment Form -->
                    <div class="comment-form-section">
                        <h4 class="form-title">Leave a Comment</h4>
                        <form class="comment-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="comment-name" class="form-label">Name *</label>
                                    <input type="text" class="form-control" id="comment-name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="comment-email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="comment-email" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="comment-website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="comment-website">
                            </div>
                            <div class="mb-4">
                                <label for="comment-message" class="form-label">Comment *</label>
                                <textarea class="form-control" id="comment-message" rows="5" required placeholder="Share your thoughts..."></textarea>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="save-info">
                                    <label class="form-check-label" for="save-info">
                                        Save my name, email, and website in this browser for the next time I comment.
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn-hero primary">
                                <i class="fas fa-comment me-2"></i>
                                Post Comment
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar">
                    <!-- Search Widget -->
                    <div class="sidebar-widget search-widget" data-aos="fade-left">
                        <h4 class="widget-title">Search</h4>
                        <form class="search-form">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Type to search...">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Recent Posts Widget -->
                    <div class="sidebar-widget recent-posts-widget" data-aos="fade-left" data-aos-delay="100">
                        <h4 class="widget-title">Recent Posts</h4>
                        <div class="recent-posts">
                            <div class="recent-post-item">
                                <div class="recent-post-image">
                                    <div class="post-thumb"></div>
                                </div>
                                <div class="recent-post-content">
                                    <h6 class="recent-post-title">
                                        <a href="#">The Future of Web Development</a>
                                    </h6>
                                    <div class="recent-post-meta">
                                        <span class="post-date">May 10, 2024</span>
                                    </div>
                                </div>
                            </div>

                            <div class="recent-post-item">
                                <div class="recent-post-image">
                                    <div class="post-thumb"></div>
                                </div>
                                <div class="recent-post-content">
                                    <h6 class="recent-post-title">
                                        <a href="#">Design Trends That Matter</a>
                                    </h6>
                                    <div class="recent-post-meta">
                                        <span class="post-date">May 8, 2024</span>
                                    </div>
                                </div>
                            </div>

                            <div class="recent-post-item">
                                <div class="recent-post-image">
                                    <div class="post-thumb"></div>
                                </div>
                                <div class="recent-post-content">
                                    <h6 class="recent-post-title">
                                        <a href="#">Amazing Travel Destinations</a>
                                    </h6>
                                    <div class="recent-post-meta">
                                        <span class="post-date">May 6, 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Categories Widget -->
                    <div class="sidebar-widget categories-widget" data-aos="fade-left" data-aos-delay="200">
                        <h4 class="widget-title">Categories</h4>
                        <ul class="categories-list">
                            <li><a href="{{ route('blog.category', 'lifestyle') }}">Lifestyle <span>(12)</span></a></li>
                            <li><a href="{{ route('blog.category', 'business') }}">Business <span>(22)</span></a></li>
                            <li><a href="{{ route('blog.category', 'technology') }}">Technology <span>(25)</span></a></li>
                            <li><a href="{{ route('blog.category', 'travel') }}">Travel <span>(17)</span></a></li>
                        </ul>
                    </div>

                    <!-- Tags Widget -->
                    <div class="sidebar-widget tags-widget" data-aos="fade-left" data-aos-delay="300">
                        <h4 class="widget-title">Tag Clouds</h4>
                        <div class="tags-cloud">
                            <a href="#" class="tag">HTML5</a>
                            <a href="#" class="tag">CSS3</a>
                            <a href="#" class="tag">Template</a>
                            <a href="#" class="tag">Responsive</a>
                            <a href="#" class="tag">Modern</a>
                            <a href="#" class="tag">Design</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
